using GhalibChatApp.Models;

namespace GhalibChatApp.Repositories
{
    public interface IMessageRepository : IRepository<Message>
    {
        Task<IEnumerable<Message>> GetChatMessagesAsync(int chatId, int page = 1, int pageSize = 50);
        Task<Message?> GetMessageWithSenderAsync(int messageId);
        Task<bool> MarkAsReadAsync(int messageId);
        Task<int> GetUnreadCountAsync(int chatId, string userId);
    }
}
