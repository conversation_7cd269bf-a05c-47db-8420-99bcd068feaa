using Microsoft.AspNetCore.SignalR;
using Microsoft.AspNetCore.Authorization;
using GhalibChatApp.Services;
using GhalibChatApp.Models;
using System.Security.Claims;

namespace GhalibChatApp.Hubs
{
    [Authorize]
    public class ChatHub : Hub
    {
        private readonly IMessageService _messageService;
        private readonly IChatService _chatService;

        public ChatHub(IMessageService messageService, IChatService chatService)
        {
            _messageService = messageService;
            _chatService = chatService;
        }

        public async Task JoinChat(string chatId)
        {
            var userId = Context.UserIdentifier;
            if (userId != null && int.TryParse(chatId, out var chatIdInt))
            {
                var isInChat = await _chatService.IsUserInChatAsync(chatIdInt, userId);
                if (isInChat)
                {
                    await Groups.AddToGroupAsync(Context.ConnectionId, $"Chat_{chatId}");
                    await Clients.Group($"Chat_{chatId}").SendAsync("UserJoined", Context.User?.Identity?.Name);
                }
            }
        }

        public async Task LeaveChat(string chatId)
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"Chat_{chatId}");
            await Clients.Group($"Chat_{chatId}").SendAsync("UserLeft", Context.User?.Identity?.Name);
        }

        public async Task SendMessage(string chatId, string content)
        {
            var userId = Context.UserIdentifier;
            if (userId != null && int.TryParse(chatId, out var chatIdInt))
            {
                var isInChat = await _chatService.IsUserInChatAsync(chatIdInt, userId);
                if (isInChat)
                {
                    var message = await _messageService.SendMessageAsync(chatIdInt, userId, content);
                    if (message != null)
                    {
                        await Clients.Group($"Chat_{chatId}").SendAsync("ReceiveMessage", new
                        {
                            Id = message.Id,
                            Content = message.Content,
                            SentAt = message.SentAt,
                            SenderName = message.Sender?.UserName ?? "Unknown",
                            SenderId = message.SenderId
                        });
                    }
                }
            }
        }

        public async Task StartTyping(string chatId)
        {
            var userName = Context.User?.Identity?.Name;
            await Clients.OthersInGroup($"Chat_{chatId}").SendAsync("UserTyping", userName);
        }

        public async Task StopTyping(string chatId)
        {
            var userName = Context.User?.Identity?.Name;
            await Clients.OthersInGroup($"Chat_{chatId}").SendAsync("UserStoppedTyping", userName);
        }

        public override async Task OnConnectedAsync()
        {
            var userId = Context.UserIdentifier;
            if (userId != null)
            {
                // Update user online status
                await Clients.All.SendAsync("UserOnline", userId);
            }
            await base.OnConnectedAsync();
        }

        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            var userId = Context.UserIdentifier;
            if (userId != null)
            {
                // Update user offline status
                await Clients.All.SendAsync("UserOffline", userId);
            }
            await base.OnDisconnectedAsync(exception);
        }
    }
}
