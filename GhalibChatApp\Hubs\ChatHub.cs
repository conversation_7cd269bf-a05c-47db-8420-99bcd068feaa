using Microsoft.AspNetCore.SignalR;
using GhalibChatApp.Services;
using GhalibChatApp.Models;
using System.Security.Claims;

namespace GhalibChatApp.Hubs
{
    public class ChatHub : Hub
    {
        private readonly IMessageService _messageService;
        private readonly IChatService _chatService;

        public ChatHub(IMessageService messageService, IChatService chatService)
        {
            _messageService = messageService;
            _chatService = chatService;
        }

        public async Task JoinChat(string chatId)
        {
            try
            {
                var userId = Context.UserIdentifier ?? Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (userId != null && int.TryParse(chatId, out var chatIdInt))
                {
                    // For now, allow all users to join any chat for testing
                    await Groups.AddToGroupAsync(Context.ConnectionId, $"Chat_{chatId}");
                    await Clients.Group($"Chat_{chatId}").SendAsync("UserJoined", Context.User?.Identity?.Name ?? "Anonymous");
                }
            }
            catch (Exception ex)
            {
                // Log error but don't throw to avoid breaking the connection
                Console.WriteLine($"Error joining chat: {ex.Message}");
            }
        }

        public async Task LeaveChat(string chatId)
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"Chat_{chatId}");
            await Clients.Group($"Chat_{chatId}").SendAsync("UserLeft", Context.User?.Identity?.Name);
        }

        public async Task SendMessage(string chatId, string content)
        {
            try
            {
                var userId = Context.UserIdentifier ?? Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (userId != null && int.TryParse(chatId, out var chatIdInt))
                {
                    // For testing, allow sending messages without strict validation
                    var message = await _messageService.SendMessageAsync(chatIdInt, userId, content);
                    if (message != null)
                    {
                        await Clients.Group($"Chat_{chatId}").SendAsync("ReceiveMessage", new
                        {
                            message.Id,
                            message.Content,
                            message.SentAt,
                            SenderName = message.Sender?.UserName ?? "Unknown",
                            message.SenderId
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error sending message: {ex.Message}");
            }
        }

        public async Task StartTyping(string chatId)
        {
            var userName = Context.User?.Identity?.Name;
            await Clients.OthersInGroup($"Chat_{chatId}").SendAsync("UserTyping", userName);
        }

        public async Task StopTyping(string chatId)
        {
            var userName = Context.User?.Identity?.Name;
            await Clients.OthersInGroup($"Chat_{chatId}").SendAsync("UserStoppedTyping", userName);
        }

        public override async Task OnConnectedAsync()
        {
            var userId = Context.UserIdentifier;
            if (userId != null)
            {
                // Update user online status
                await Clients.All.SendAsync("UserOnline", userId);
            }
            await base.OnConnectedAsync();
        }

        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            var userId = Context.UserIdentifier;
            if (userId != null)
            {
                // Update user offline status
                await Clients.All.SendAsync("UserOffline", userId);
            }
            await base.OnDisconnectedAsync(exception);
        }

        // Test methods for debugging
        public async Task JoinTestChat()
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, "TestChat");
            await Clients.Group("TestChat").SendAsync("ReceiveTestMessage", "System", $"User {Context.ConnectionId} joined the test chat");
        }

        public async Task SendTestMessage(string user, string message)
        {
            await Clients.Group("TestChat").SendAsync("ReceiveTestMessage", user, message);
        }
    }
}
