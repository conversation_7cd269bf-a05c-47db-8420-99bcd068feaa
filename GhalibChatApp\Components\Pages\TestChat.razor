@page "/testchat"
@using Microsoft.AspNetCore.SignalR.Client
@inject NavigationManager Navigation
@implements IAsyncDisposable

<PageTitle>Test Chat - Simple Version</PageTitle>

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h2>Simple Chat Test</h2>
            <p>Connection Status: <strong class="@(IsConnected ? "text-success" : "text-danger")">@(IsConnected ? "Connected" : "Disconnected")</strong></p>
            
            @if (!IsConnected)
            {
                <button class="btn btn-primary" @onclick="ConnectAsync">Connect to Chat</button>
            }
            else
            {
                <button class="btn btn-danger" @onclick="DisconnectAsync">Disconnect</button>
            }
        </div>
    </div>

    @if (IsConnected)
    {
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Chat Room</h5>
                    </div>
                    <div class="card-body" style="height: 400px; overflow-y: auto;" @ref="messagesContainer">
                        @foreach (var message in messages)
                        {
                            <div class="mb-2">
                                <small class="text-muted">@message.Timestamp.ToString("HH:mm:ss")</small>
                                <strong>@message.User:</strong> @message.Content
                            </div>
                        }
                    </div>
                    <div class="card-footer">
                        <div class="input-group">
                            <input type="text" class="form-control" @bind="messageInput" @onkeypress="@(async (e) => { if (e.Key == "Enter") await SendAsync(); })" placeholder="Type a message..." />
                            <button class="btn btn-primary" @onclick="SendAsync">Send</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private HubConnection? hubConnection;
    private List<ChatMessage> messages = new();
    private string messageInput = string.Empty;
    private ElementReference messagesContainer;

    public bool IsConnected =>
        hubConnection?.State == HubConnectionState.Connected;

    private async Task ConnectAsync()
    {
        hubConnection = new HubConnectionBuilder()
            .WithUrl(Navigation.ToAbsoluteUri("/chathub"))
            .Build();

        hubConnection.On<string, string>("ReceiveTestMessage", (user, message) =>
        {
            var newMessage = new ChatMessage
            {
                User = user,
                Content = message,
                Timestamp = DateTime.Now
            };
            
            InvokeAsync(() =>
            {
                messages.Add(newMessage);
                StateHasChanged();
                ScrollToBottom();
            });
        });

        try
        {
            await hubConnection.StartAsync();
            StateHasChanged();
            
            // Join a test chat room
            await hubConnection.SendAsync("JoinTestChat");
        }
        catch (Exception ex)
        {
            messages.Add(new ChatMessage 
            { 
                User = "System", 
                Content = $"Connection failed: {ex.Message}", 
                Timestamp = DateTime.Now 
            });
            StateHasChanged();
        }
    }

    private async Task DisconnectAsync()
    {
        if (hubConnection is not null)
        {
            await hubConnection.DisposeAsync();
            hubConnection = null;
            StateHasChanged();
        }
    }

    private async Task SendAsync()
    {
        if (hubConnection is not null && !string.IsNullOrWhiteSpace(messageInput))
        {
            try
            {
                await hubConnection.SendAsync("SendTestMessage", "TestUser", messageInput);
                messageInput = string.Empty;
            }
            catch (Exception ex)
            {
                messages.Add(new ChatMessage 
                { 
                    User = "System", 
                    Content = $"Send failed: {ex.Message}", 
                    Timestamp = DateTime.Now 
                });
                StateHasChanged();
            }
        }
    }

    private async Task ScrollToBottom()
    {
        await Task.Delay(50);
        await messagesContainer.FocusAsync();
    }

    public async ValueTask DisposeAsync()
    {
        if (hubConnection is not null)
        {
            await hubConnection.DisposeAsync();
        }
    }

    public class ChatMessage
    {
        public string User { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
    }
}
