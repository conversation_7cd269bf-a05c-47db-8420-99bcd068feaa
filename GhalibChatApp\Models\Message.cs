using System.ComponentModel.DataAnnotations;
using GhalibChatApp.Data;

namespace GhalibChatApp.Models
{
    public class Message
    {
        public int Id { get; set; }
        
        [Required]
        public string Content { get; set; } = string.Empty;
        
        public DateTime SentAt { get; set; } = DateTime.UtcNow;
        
        public bool IsRead { get; set; } = false;
        
        public bool IsEdited { get; set; } = false;
        
        public DateTime? EditedAt { get; set; }
        
        public MessageType Type { get; set; } = MessageType.Text;
        
        public string? AttachmentUrl { get; set; }
        
        // Foreign keys
        public int ChatId { get; set; }
        public string SenderId { get; set; } = string.Empty;
        
        // Navigation properties
        public virtual Chat Chat { get; set; } = null!;
        public virtual ApplicationUser Sender { get; set; } = null!;
    }
    
    public enum MessageType
    {
        Text,
        Image,
        File,
        Audio,
        Video
    }
}
