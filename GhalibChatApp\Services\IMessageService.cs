using GhalibChatApp.Models;

namespace GhalibChatApp.Services
{
    public interface IMessageService
    {
        Task<IEnumerable<Message>> GetChatMessagesAsync(int chatId, int page = 1, int pageSize = 50);
        Task<Message?> SendMessageAsync(int chatId, string senderId, string content, MessageType type = MessageType.Text);
        Task<Message?> GetMessageAsync(int messageId);
        Task<bool> EditMessageAsync(int messageId, string newContent);
        Task<bool> DeleteMessageAsync(int messageId);
        Task<bool> MarkAsReadAsync(int messageId);
        Task<int> GetUnreadCountAsync(int chatId, string userId);
    }
}
