using GhalibChatApp.Models;
using GhalibChatApp.Repositories;

namespace GhalibChatApp.Services
{
    public class MessageService : IMessageService
    {
        private readonly IMessageRepository _messageRepository;

        public MessageService(IMessageRepository messageRepository)
        {
            _messageRepository = messageRepository;
        }

        public async Task<IEnumerable<Message>> GetChatMessagesAsync(int chatId, int page = 1, int pageSize = 50)
        {
            return await _messageRepository.GetChatMessagesAsync(chatId, page, pageSize);
        }

        public async Task<Message?> SendMessageAsync(int chatId, string senderId, string content, MessageType type = MessageType.Text)
        {
            var message = new Message
            {
                Content = content,
                ChatId = chatId,
                SenderId = senderId,
                Type = type
            };

            var messageId = await _messageRepository.AddAsync(message);
            if (messageId > 0)
            {
                message.Id = messageId;
                return await _messageRepository.GetMessageWithSenderAsync(messageId);
            }

            return null;
        }

        public async Task<Message?> GetMessageAsync(int messageId)
        {
            return await _messageRepository.GetMessageWithSenderAsync(messageId);
        }

        public async Task<bool> EditMessageAsync(int messageId, string newContent)
        {
            var message = await _messageRepository.GetByIdAsync(messageId);
            if (message != null)
            {
                message.Content = newContent;
                return await _messageRepository.UpdateAsync(message);
            }
            return false;
        }

        public async Task<bool> DeleteMessageAsync(int messageId)
        {
            return await _messageRepository.DeleteAsync(messageId);
        }

        public async Task<bool> MarkAsReadAsync(int messageId)
        {
            return await _messageRepository.MarkAsReadAsync(messageId);
        }

        public async Task<int> GetUnreadCountAsync(int chatId, string userId)
        {
            return await _messageRepository.GetUnreadCountAsync(chatId, userId);
        }
    }
}
