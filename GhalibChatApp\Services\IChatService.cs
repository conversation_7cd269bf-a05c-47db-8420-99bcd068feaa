using GhalibChatApp.Models;

namespace GhalibChatApp.Services
{
    public interface IChatService
    {
        Task<IEnumerable<Chat>> GetUserChatsAsync(string userId);
        Task<Chat?> GetChatAsync(int chatId);
        Task<Chat?> CreateChatAsync(string name, string createdById, bool isGroup = false, string? description = null);
        Task<bool> AddParticipantAsync(int chatId, string userId, bool isAdmin = false);
        Task<bool> RemoveParticipantAsync(int chatId, string userId);
        Task<bool> IsUserInChatAsync(int chatId, string userId);
        Task<Chat?> CreateDirectChatAsync(string user1Id, string user2Id);
    }
}
