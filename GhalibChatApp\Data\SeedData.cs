using Microsoft.AspNetCore.Identity;
using GhalibChatApp.Models;
using GhalibChatApp.Services;

namespace GhalibChatApp.Data
{
    public static class SeedData
    {
        public static async Task InitializeAsync(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var userManager = scope.ServiceProvider.GetRequiredService<UserManager<ApplicationUser>>();
            var chatService = scope.ServiceProvider.GetRequiredService<IChatService>();

            // Create test users if they don't exist
            var user1 = await userManager.FindByEmailAsync("<EMAIL>");
            if (user1 == null)
            {
                user1 = new ApplicationUser
                {
                    UserName = "<EMAIL>",
                    Email = "<EMAIL>",
                    FirstName = "John",
                    LastName = "Doe",
                    EmailConfirmed = true
                };
                await userManager.CreateAsync(user1, "Password123!");
            }

            var user2 = await userManager.FindByEmailAsync("<EMAIL>");
            if (user2 == null)
            {
                user2 = new ApplicationUser
                {
                    UserName = "<EMAIL>",
                    Email = "<EMAIL>",
                    FirstName = "Jane",
                    LastName = "Smith",
                    EmailConfirmed = true
                };
                await userManager.CreateAsync(user2, "Password123!");
            }

            // Create a sample chat
            var existingChats = await chatService.GetUserChatsAsync(user1.Id);
            if (!existingChats.Any())
            {
                var chat = await chatService.CreateChatAsync("General Chat", user1.Id, true, "A general discussion chat");
                if (chat != null)
                {
                    await chatService.AddParticipantAsync(chat.Id, user2.Id);
                }
            }
        }
    }
}
