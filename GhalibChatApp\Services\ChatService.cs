using GhalibChatApp.Models;
using GhalibChatApp.Repositories;

namespace GhalibChatApp.Services
{
    public class ChatService : IChatService
    {
        private readonly IChatRepository _chatRepository;

        public ChatService(IChatRepository chatRepository)
        {
            _chatRepository = chatRepository;
        }

        public async Task<IEnumerable<Chat>> GetUserChatsAsync(string userId)
        {
            return await _chatRepository.GetUserChatsAsync(userId);
        }

        public async Task<Chat?> GetChatAsync(int chatId)
        {
            return await _chatRepository.GetChatWithParticipantsAsync(chatId);
        }

        public async Task<Chat?> CreateChatAsync(string name, string createdById, bool isGroup = false, string? description = null)
        {
            var chat = new Chat
            {
                Name = name,
                Description = description,
                IsGroup = isGroup,
                CreatedById = createdById
            };

            var chatId = await _chatRepository.AddAsync(chat);
            if (chatId > 0)
            {
                // Add creator as admin participant
                await _chatRepository.AddParticipantAsync(chatId, createdById, true);
                chat.Id = chatId;
                return chat;
            }

            return null;
        }

        public async Task<bool> AddParticipantAsync(int chatId, string userId, bool isAdmin = false)
        {
            return await _chatRepository.AddParticipantAsync(chatId, userId, isAdmin);
        }

        public async Task<bool> RemoveParticipantAsync(int chatId, string userId)
        {
            return await _chatRepository.RemoveParticipantAsync(chatId, userId);
        }

        public async Task<bool> IsUserInChatAsync(int chatId, string userId)
        {
            return await _chatRepository.IsUserInChatAsync(chatId, userId);
        }

        public async Task<Chat?> CreateDirectChatAsync(string user1Id, string user2Id)
        {
            // For direct chats, we can use a naming convention or check if chat already exists
            var chatName = $"Direct_{user1Id}_{user2Id}";
            
            var chat = new Chat
            {
                Name = chatName,
                IsGroup = false,
                CreatedById = user1Id
            };

            var chatId = await _chatRepository.AddAsync(chat);
            if (chatId > 0)
            {
                // Add both users as participants
                await _chatRepository.AddParticipantAsync(chatId, user1Id, false);
                await _chatRepository.AddParticipantAsync(chatId, user2Id, false);
                chat.Id = chatId;
                return chat;
            }

            return null;
        }
    }
}
