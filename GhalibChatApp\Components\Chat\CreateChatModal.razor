@using GhalibChatApp.Services
@using GhalibChatApp.Models
@using System.Security.Claims
@using System.ComponentModel.DataAnnotations
@inject IChatService ChatService
@inject AuthenticationStateProvider AuthenticationStateProvider

<div class="modal fade @(IsVisible ? "show" : "")" style="display: @(IsVisible ? "block" : "none")" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Chat</h5>
                <button type="button" class="btn-close" @onclick="Close"></button>
            </div>
            <div class="modal-body">
                <EditForm Model="@chatModel" OnValidSubmit="@CreateChat">
                    <DataAnnotationsValidator />
                    <ValidationSummary />
                    
                    <div class="mb-3">
                        <label for="chatName" class="form-label">Chat Name</label>
                        <InputText id="chatName" class="form-control" @bind-Value="chatModel.Name" />
                        <ValidationMessage For="@(() => chatModel.Name)" />
                    </div>
                    
                    <div class="mb-3">
                        <label for="chatDescription" class="form-label">Description (Optional)</label>
                        <InputTextArea id="chatDescription" class="form-control" @bind-Value="chatModel.Description" />
                    </div>
                    
                    <div class="mb-3 form-check">
                        <InputCheckbox id="isGroup" class="form-check-input" @bind-Value="chatModel.IsGroup" />
                        <label class="form-check-label" for="isGroup">
                            Group Chat
                        </label>
                    </div>
                    
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" @onclick="Close">Cancel</button>
                        <button type="submit" class="btn btn-primary" disabled="@isCreating">
                            @if (isCreating)
                            {
                                <span class="spinner-border spinner-border-sm" role="status"></span>
                                <span>Creating...</span>
                            }
                            else
                            {
                                <span>Create Chat</span>
                            }
                        </button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
</div>

@if (IsVisible)
{
    <div class="modal-backdrop fade show"></div>
}

@code {
    [Parameter] public bool IsVisible { get; set; }
    [Parameter] public EventCallback OnChatCreated { get; set; }
    [Parameter] public EventCallback OnClose { get; set; }

    private CreateChatModel chatModel = new();
    private bool isCreating = false;

    private async Task CreateChat()
    {
        isCreating = true;
        try
        {
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var userId = authState.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

            if (userId != null)
            {
                var chat = await ChatService.CreateChatAsync(
                    chatModel.Name, 
                    userId, 
                    chatModel.IsGroup, 
                    chatModel.Description);

                if (chat != null)
                {
                    await OnChatCreated.InvokeAsync();
                    Close();
                    chatModel = new(); // Reset form
                }
            }
        }
        finally
        {
            isCreating = false;
        }
    }

    private async Task Close()
    {
        await OnClose.InvokeAsync();
    }

    public class CreateChatModel
    {
        [Required]
        [StringLength(100, MinimumLength = 1)]
        public string Name { get; set; } = string.Empty;
        
        public string? Description { get; set; }
        
        public bool IsGroup { get; set; } = false;
    }
}
