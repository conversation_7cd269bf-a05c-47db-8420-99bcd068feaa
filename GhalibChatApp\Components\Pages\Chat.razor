@page "/chat"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.SignalR.Client
@using GhalibChatApp.Services
@using GhalibChatApp.Models
@using System.Security.Claims
@attribute [Authorize]
@inject IChatService ChatService
@inject IMessageService MessageService
@inject NavigationManager Navigation
@inject AuthenticationStateProvider AuthenticationStateProvider
@implements IAsyncDisposable

<PageTitle>Chat - Ghalib Chat App</PageTitle>

<div class="chat-container">
    <div class="chat-sidebar">
        <div class="chat-header">
            <h3>Chats</h3>
            <button class="btn btn-primary btn-sm" @onclick="ShowCreateChatModal">New Chat</button>
        </div>
        <div class="chat-list">
            @if (userChats != null)
            {
                @foreach (var chat in userChats)
                {
                    <div class="chat-item @(selectedChatId == chat.Id ? "active" : "")" 
                         @onclick="() => SelectChat(chat.Id)">
                        <div class="chat-avatar">
                            @if (!string.IsNullOrEmpty(chat.ImageUrl))
                            {
                                <img src="@chat.ImageUrl" alt="@chat.Name" />
                            }
                            else
                            {
                                <div class="avatar-placeholder">@chat.Name.Substring(0, 1).ToUpper()</div>
                            }
                        </div>
                        <div class="chat-info">
                            <div class="chat-name">@chat.Name</div>
                            <div class="chat-description">@chat.Description</div>
                        </div>
                    </div>
                }
            }
        </div>
    </div>

    <div class="chat-main">
        @if (selectedChatId.HasValue)
        {
            <div class="chat-messages-header">
                <h4>@selectedChat?.Name</h4>
            </div>
            
            <div class="chat-messages" @ref="messagesContainer">
                @if (messages != null)
                {
                    @foreach (var message in messages)
                    {
                        <div class="message @(message.SenderId == currentUserId ? "message-sent" : "message-received")">
                            <div class="message-sender">@message.Sender?.UserName</div>
                            <div class="message-content">@message.Content</div>
                            <div class="message-time">@message.SentAt.ToString("HH:mm")</div>
                        </div>
                    }
                }
            </div>

            <div class="chat-input">
                <div class="input-group">
                    <input type="text" class="form-control" @bind="newMessage" @onkeypress="HandleKeyPress" 
                           placeholder="Type a message..." />
                    <button class="btn btn-primary" @onclick="SendMessage" disabled="@(!IsConnected)">
                        Send
                    </button>
                </div>
            </div>
        }
        else
        {
            <div class="no-chat-selected">
                <h4>Select a chat to start messaging</h4>
            </div>
        }
    </div>
</div>

<GhalibChatApp.Components.Chat.CreateChatModal
    IsVisible="@showCreateChatModal"
    OnChatCreated="@OnChatCreated"
    OnClose="@(() => showCreateChatModal = false)" />

@code {
    private HubConnection? hubConnection;
    private List<GhalibChatApp.Models.Chat>? userChats;
    private List<GhalibChatApp.Models.Message>? messages;
    private GhalibChatApp.Models.Chat? selectedChat;
    private int? selectedChatId;
    private string newMessage = string.Empty;
    private string? currentUserId;
    private ElementReference messagesContainer;
    private bool showCreateChatModal = false;

    public bool IsConnected =>
        hubConnection?.State == HubConnectionState.Connected;

    protected override async Task OnInitializedAsync()
    {
        // Get current user
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        currentUserId = authState.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

        // Only proceed if user is authenticated
        if (currentUserId != null && authState.User.Identity?.IsAuthenticated == true)
        {
            // Load user chats
            userChats = (await ChatService.GetUserChatsAsync(currentUserId)).ToList();

            // Initialize SignalR connection
            await InitializeSignalRConnection();
        }
    }

    private async Task InitializeSignalRConnection()
    {
        try
        {
            hubConnection = new HubConnectionBuilder()
                .WithUrl(Navigation.ToAbsoluteUri("/chathub"))
                .Build();

            hubConnection.On<object>("ReceiveMessage", (messageData) =>
            {
                InvokeAsync(() =>
                {
                    // Handle received message
                    StateHasChanged();
                    ScrollToBottom();
                });
            });

            await hubConnection.StartAsync();
        }
        catch (Exception ex)
        {
            // Handle connection errors gracefully
            Console.WriteLine($"SignalR connection error: {ex.Message}");
        }
    }

    private async Task SelectChat(int chatId)
    {
        try
        {
            if (selectedChatId.HasValue && IsConnected)
            {
                await hubConnection!.SendAsync("LeaveChat", selectedChatId.Value.ToString());
            }

            selectedChatId = chatId;
            selectedChat = await ChatService.GetChatAsync(chatId);
            messages = (await MessageService.GetChatMessagesAsync(chatId)).ToList();

            if (IsConnected)
            {
                await hubConnection!.SendAsync("JoinChat", chatId.ToString());
            }

            StateHasChanged();
            await ScrollToBottom();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error selecting chat: {ex.Message}");
        }
    }

    private async Task SendMessage()
    {
        try
        {
            if (!string.IsNullOrWhiteSpace(newMessage) && selectedChatId.HasValue && IsConnected)
            {
                await hubConnection!.SendAsync("SendMessage", selectedChatId.Value.ToString(), newMessage);
                newMessage = string.Empty;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error sending message: {ex.Message}");
        }
    }

    private async Task HandleKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await SendMessage();
        }
    }

    private async Task ScrollToBottom()
    {
        await Task.Delay(50); // Small delay to ensure DOM is updated
        await messagesContainer.FocusAsync();
    }

    private void ShowCreateChatModal()
    {
        showCreateChatModal = true;
    }

    private async Task OnChatCreated()
    {
        // Reload user chats
        if (currentUserId != null)
        {
            userChats = (await ChatService.GetUserChatsAsync(currentUserId)).ToList();
            StateHasChanged();
        }
    }

    public async ValueTask DisposeAsync()
    {
        if (hubConnection is not null)
        {
            await hubConnection.DisposeAsync();
        }
    }
}

<style>
    .chat-container {
        display: flex;
        height: calc(100vh - 100px);
        border: 1px solid #ddd;
        border-radius: 8px;
        overflow: hidden;
    }

    .chat-sidebar {
        width: 300px;
        border-right: 1px solid #ddd;
        background-color: #f8f9fa;
    }

    .chat-header {
        padding: 1rem;
        border-bottom: 1px solid #ddd;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .chat-list {
        overflow-y: auto;
        height: calc(100% - 80px);
    }

    .chat-item {
        display: flex;
        padding: 1rem;
        border-bottom: 1px solid #eee;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .chat-item:hover {
        background-color: #e9ecef;
    }

    .chat-item.active {
        background-color: #007bff;
        color: white;
    }

    .chat-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 1rem;
        overflow: hidden;
    }

    .avatar-placeholder {
        width: 100%;
        height: 100%;
        background-color: #6c757d;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
    }

    .chat-info {
        flex: 1;
    }

    .chat-name {
        font-weight: bold;
        margin-bottom: 0.25rem;
    }

    .chat-description {
        font-size: 0.875rem;
        color: #6c757d;
    }

    .chat-main {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .chat-messages-header {
        padding: 1rem;
        border-bottom: 1px solid #ddd;
        background-color: #f8f9fa;
    }

    .chat-messages {
        flex: 1;
        padding: 1rem;
        overflow-y: auto;
        background-color: white;
    }

    .message {
        margin-bottom: 1rem;
        max-width: 70%;
    }

    .message-sent {
        margin-left: auto;
        text-align: right;
    }

    .message-received {
        margin-right: auto;
    }

    .message-sender {
        font-size: 0.75rem;
        color: #6c757d;
        margin-bottom: 0.25rem;
    }

    .message-content {
        background-color: #e9ecef;
        padding: 0.5rem 1rem;
        border-radius: 1rem;
        display: inline-block;
    }

    .message-sent .message-content {
        background-color: #007bff;
        color: white;
    }

    .message-time {
        font-size: 0.75rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }

    .chat-input {
        padding: 1rem;
        border-top: 1px solid #ddd;
        background-color: #f8f9fa;
    }

    .no-chat-selected {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #6c757d;
    }
</style>
