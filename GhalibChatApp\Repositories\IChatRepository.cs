using GhalibChatApp.Models;

namespace GhalibChatApp.Repositories
{
    public interface IChatRepository : IRepository<Chat>
    {
        Task<IEnumerable<Chat>> GetUserChatsAsync(string userId);
        Task<Chat?> GetChatWithParticipantsAsync(int chatId);
        Task<bool> AddParticipantAsync(int chatId, string userId, bool isAdmin = false);
        Task<bool> RemoveParticipantAsync(int chatId, string userId);
        Task<bool> IsUserInChatAsync(int chatId, string userId);
    }
}
