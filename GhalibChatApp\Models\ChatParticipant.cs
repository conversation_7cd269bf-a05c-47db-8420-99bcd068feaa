using GhalibChatApp.Data;

namespace GhalibChatApp.Models
{
    public class ChatParticipant
    {
        public int Id { get; set; }
        
        public int ChatId { get; set; }
        public string UserId { get; set; } = string.Empty;
        
        public DateTime JoinedAt { get; set; } = DateTime.UtcNow;
        
        public bool IsAdmin { get; set; } = false;
        
        public bool IsMuted { get; set; } = false;
        
        public DateTime? LastSeenAt { get; set; }
        
        // Navigation properties
        public virtual Chat Chat { get; set; } = null!;
        public virtual ApplicationUser User { get; set; } = null!;
    }
}
