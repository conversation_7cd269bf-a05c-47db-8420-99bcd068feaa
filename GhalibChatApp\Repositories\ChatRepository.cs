using Dapper;
using GhalibChatApp.Models;
using GhalibChatApp.Data;
using Microsoft.Data.SqlClient;

namespace GhalibChatApp.Repositories
{
    public class ChatRepository : IChatRepository
    {
        private readonly string _connectionString;

        public ChatRepository(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection") 
                ?? throw new InvalidOperationException("Connection string not found.");
        }

        public async Task<IEnumerable<Chat>> GetAllAsync()
        {
            using var connection = new SqlConnection(_connectionString);
            const string sql = "SELECT * FROM Chats ORDER BY CreatedAt DESC";
            return await connection.QueryAsync<Chat>(sql);
        }

        public async Task<Chat?> GetByIdAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);
            const string sql = "SELECT * FROM Chats WHERE Id = @Id";
            return await connection.QueryFirstOrDefaultAsync<Chat>(sql, new { Id = id });
        }

        public async Task<int> AddAsync(Chat entity)
        {
            using var connection = new SqlConnection(_connectionString);
            const string sql = @"
                INSERT INTO Chats (Name, Description, IsGroup, ImageUrl, CreatedById)
                VALUES (@Name, @Description, @IsGroup, @ImageUrl, @CreatedById);
                SELECT CAST(SCOPE_IDENTITY() as int);";
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public async Task<bool> UpdateAsync(Chat entity)
        {
            using var connection = new SqlConnection(_connectionString);
            const string sql = @"
                UPDATE Chats 
                SET Name = @Name, Description = @Description, ImageUrl = @ImageUrl
                WHERE Id = @Id";
            var rowsAffected = await connection.ExecuteAsync(sql, entity);
            return rowsAffected > 0;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);
            const string sql = "DELETE FROM Chats WHERE Id = @Id";
            var rowsAffected = await connection.ExecuteAsync(sql, new { Id = id });
            return rowsAffected > 0;
        }

        public async Task<IEnumerable<Chat>> GetUserChatsAsync(string userId)
        {
            using var connection = new SqlConnection(_connectionString);
            const string sql = @"
                SELECT c.* FROM Chats c
                INNER JOIN ChatParticipants cp ON c.Id = cp.ChatId
                WHERE cp.UserId = @UserId
                ORDER BY c.CreatedAt DESC";
            return await connection.QueryAsync<Chat>(sql, new { UserId = userId });
        }

        public async Task<Chat?> GetChatWithParticipantsAsync(int chatId)
        {
            using var connection = new SqlConnection(_connectionString);
            const string sql = @"
                SELECT c.*, cp.*, u.Id, u.UserName, u.Email, u.FirstName, u.LastName, u.ProfilePictureUrl
                FROM Chats c
                LEFT JOIN ChatParticipants cp ON c.Id = cp.ChatId
                LEFT JOIN AspNetUsers u ON cp.UserId = u.Id
                WHERE c.Id = @ChatId";

            var chatDictionary = new Dictionary<int, Chat>();
            
            var result = await connection.QueryAsync<Chat, ChatParticipant, ApplicationUser, Chat>(
                sql,
                (chat, participant, user) =>
                {
                    if (!chatDictionary.TryGetValue(chat.Id, out var chatEntry))
                    {
                        chatEntry = chat;
                        chatEntry.Participants = new List<ChatParticipant>();
                        chatDictionary.Add(chatEntry.Id, chatEntry);
                    }

                    if (participant != null && user != null)
                    {
                        participant.User = user;
                        chatEntry.Participants.Add(participant);
                    }

                    return chatEntry;
                },
                new { ChatId = chatId },
                splitOn: "Id,Id"
            );

            return result.FirstOrDefault();
        }

        public async Task<bool> AddParticipantAsync(int chatId, string userId, bool isAdmin = false)
        {
            using var connection = new SqlConnection(_connectionString);
            const string sql = @"
                INSERT INTO ChatParticipants (ChatId, UserId, IsAdmin, IsMuted)
                VALUES (@ChatId, @UserId, @IsAdmin, @IsMuted)";
            var rowsAffected = await connection.ExecuteAsync(sql, new { ChatId = chatId, UserId = userId, IsAdmin = isAdmin, IsMuted = false });
            return rowsAffected > 0;
        }

        public async Task<bool> RemoveParticipantAsync(int chatId, string userId)
        {
            using var connection = new SqlConnection(_connectionString);
            const string sql = "DELETE FROM ChatParticipants WHERE ChatId = @ChatId AND UserId = @UserId";
            var rowsAffected = await connection.ExecuteAsync(sql, new { ChatId = chatId, UserId = userId });
            return rowsAffected > 0;
        }

        public async Task<bool> IsUserInChatAsync(int chatId, string userId)
        {
            using var connection = new SqlConnection(_connectionString);
            const string sql = "SELECT COUNT(1) FROM ChatParticipants WHERE ChatId = @ChatId AND UserId = @UserId";
            var count = await connection.QuerySingleAsync<int>(sql, new { ChatId = chatId, UserId = userId });
            return count > 0;
        }
    }
}
