using Dapper;
using GhalibChatApp.Models;
using Microsoft.Data.SqlClient;

namespace GhalibChatApp.Repositories
{
    public class MessageRepository : IMessageRepository
    {
        private readonly string _connectionString;

        public MessageRepository(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection") 
                ?? throw new InvalidOperationException("Connection string not found.");
        }

        public async Task<IEnumerable<Message>> GetAllAsync()
        {
            using var connection = new SqlConnection(_connectionString);
            const string sql = "SELECT * FROM Messages ORDER BY SentAt DESC";
            return await connection.QueryAsync<Message>(sql);
        }

        public async Task<Message?> GetByIdAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);
            const string sql = "SELECT * FROM Messages WHERE Id = @Id";
            return await connection.QueryFirstOrDefaultAsync<Message>(sql, new { Id = id });
        }

        public async Task<int> AddAsync(Message entity)
        {
            using var connection = new SqlConnection(_connectionString);
            const string sql = @"
                INSERT INTO Messages (Content, ChatId, SenderId, Type, AttachmentUrl)
                VALUES (@Content, @ChatId, @SenderId, @Type, @AttachmentUrl);
                SELECT CAST(SCOPE_IDENTITY() as int);";
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public async Task<bool> UpdateAsync(Message entity)
        {
            using var connection = new SqlConnection(_connectionString);
            const string sql = @"
                UPDATE Messages 
                SET Content = @Content, IsEdited = 1, EditedAt = GETUTCDATE()
                WHERE Id = @Id";
            var rowsAffected = await connection.ExecuteAsync(sql, entity);
            return rowsAffected > 0;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);
            const string sql = "DELETE FROM Messages WHERE Id = @Id";
            var rowsAffected = await connection.ExecuteAsync(sql, new { Id = id });
            return rowsAffected > 0;
        }

        public async Task<IEnumerable<Message>> GetChatMessagesAsync(int chatId, int page = 1, int pageSize = 50)
        {
            using var connection = new SqlConnection(_connectionString);
            const string sql = @"
                SELECT m.*, u.Id, u.UserName, u.Email, u.FirstName, u.LastName, u.ProfilePictureUrl
                FROM Messages m
                INNER JOIN AspNetUsers u ON m.SenderId = u.Id
                WHERE m.ChatId = @ChatId
                ORDER BY m.SentAt DESC
                OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";
            
            var offset = (page - 1) * pageSize;
            var result = await connection.QueryAsync<Message, ApplicationUser, Message>(
                sql,
                (message, user) =>
                {
                    message.Sender = user;
                    return message;
                },
                new { ChatId = chatId, Offset = offset, PageSize = pageSize },
                splitOn: "Id"
            );

            return result.Reverse(); // Reverse to show oldest first
        }

        public async Task<Message?> GetMessageWithSenderAsync(int messageId)
        {
            using var connection = new SqlConnection(_connectionString);
            const string sql = @"
                SELECT m.*, u.Id, u.UserName, u.Email, u.FirstName, u.LastName, u.ProfilePictureUrl
                FROM Messages m
                INNER JOIN AspNetUsers u ON m.SenderId = u.Id
                WHERE m.Id = @MessageId";
            
            var result = await connection.QueryAsync<Message, ApplicationUser, Message>(
                sql,
                (message, user) =>
                {
                    message.Sender = user;
                    return message;
                },
                new { MessageId = messageId },
                splitOn: "Id"
            );

            return result.FirstOrDefault();
        }

        public async Task<bool> MarkAsReadAsync(int messageId)
        {
            using var connection = new SqlConnection(_connectionString);
            const string sql = "UPDATE Messages SET IsRead = 1 WHERE Id = @MessageId";
            var rowsAffected = await connection.ExecuteAsync(sql, new { MessageId = messageId });
            return rowsAffected > 0;
        }

        public async Task<int> GetUnreadCountAsync(int chatId, string userId)
        {
            using var connection = new SqlConnection(_connectionString);
            const string sql = @"
                SELECT COUNT(*)
                FROM Messages m
                WHERE m.ChatId = @ChatId 
                AND m.SenderId != @UserId 
                AND m.IsRead = 0";
            return await connection.QuerySingleAsync<int>(sql, new { ChatId = chatId, UserId = userId });
        }
    }
}
