﻿@using Microsoft.AspNetCore.Identity
@using GhalibChatApp.Data

@inject SignInManager<ApplicationUser> SignInManager

<ul class="nav nav-pills flex-column">
    <li class="nav-item">
        <NavLink class="nav-link" href="Account/Manage" Match="NavLinkMatch.All">Profile</NavLink>
    </li>
    <li class="nav-item">
        <NavLink class="nav-link" href="Account/Manage/Email">Email</NavLink>
    </li>
    <li class="nav-item">
        <NavLink class="nav-link" href="Account/Manage/ChangePassword">Password</NavLink>
    </li>
    @if (hasExternalLogins)
    {
        <li class="nav-item">
            <NavLink class="nav-link" href="Account/Manage/ExternalLogins">External logins</NavLink>
        </li>
    }
    <li class="nav-item">
        <NavLink class="nav-link" href="Account/Manage/TwoFactorAuthentication">Two-factor authentication</NavLink>
    </li>
    <li class="nav-item">
        <NavLink class="nav-link" href="Account/Manage/PersonalData">Personal data</NavLink>
    </li>
</ul>

@code {
    private bool hasExternalLogins;

    protected override async Task OnInitializedAsync()
    {
        hasExternalLogins = (await SignInManager.GetExternalAuthenticationSchemesAsync()).Any();
    }
}
